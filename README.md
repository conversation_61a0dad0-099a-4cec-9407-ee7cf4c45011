# ADAM_DL_Backend

This project extracts Java code lineage from a project and uploads it to Neo4j as a graph database.

---

## ⚙️ Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/brillio-Oneai/ADAM_DL_Backend.git
cd ADAM_DL_Backend
```

### 2. Create and Activate a Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt

```

### 4. Set Up Environment Variables
Create a ".env" file in the root directory with the following content:

```bash 
# .env

# this is template, fill with your details 
NEO4J_URI=bolt://localhost:7687  
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
NEO4J_DB=your_db_name
PROJECT_PATH=******** # give the main folder path here
OUTPUT_JSON=********  # give a json name here 
```
### 5. Run the Script

Once the environment is set up, run the main script:

```bash
python main.py
```

This will:

Parse Java files from the path specified in PROJECT_PATH

Generate a JSON file with node and relationship data

Load the graph into the Neo4j database configured in .env
