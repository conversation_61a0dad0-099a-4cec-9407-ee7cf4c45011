import logging
from config import *
from extractor.java_parser import extract_relations_to_json_javalang
from extractor.json_handler import save_json
from neo4j_loader.loader import load_json_to_neo4j

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

def main():
    logging.info("Starting Java Lineage Extraction...")

    graph_data = extract_relations_to_json_javalang(PROJECT_PATH)
    logging.info("Extraction complete. Saving to JSON...")

    save_json(graph_data, OUTPUT_JSON)
    logging.info(f"JSON saved to {OUTPUT_JSON}")

    logging.info("Loading graph into Neo4j...")
    load_json_to_neo4j(OUTPUT_JSON, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)
    
    logging.info("Extraction and loading complete.")


if __name__ == "__main__":
    main()
