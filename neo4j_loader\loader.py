import json
import logging
import time
from neo4j import GraphDatabase

def load_json_to_neo4j(json_path, uri, user, password, db):
    with open(json_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    nodes = data["nodes"]
    relations = data["relations"]

    driver = GraphDatabase.driver(uri, auth=(user, password))

    def import_data(tx):
        start_nodes = time.time()
        for node in nodes:
            label = node["type"].capitalize()
            tx.run(f"""
                MERGE (n:{label} {{id: $id}})
                SET n.name = $name,
                    n.full_name = $full_name
            """, id=node["id"], name=node["name"], full_name=node["full_name"])
        end_nodes = time.time()
        logging.info(f"Imported {len(nodes)} nodes in {end_nodes - start_nodes:.2f} seconds")

        start_rels = time.time()
        for src_id, rel_type, dst_id in relations:
            tx.run(f"""
                MATCH (src {{id: $src_id}})
                MATCH (dst {{id: $dst_id}})
                MERGE (src)-[:`{rel_type}`]->(dst)
            """, src_id=src_id, dst_id=dst_id)
        end_rels = time.time()
        logging.info(f"Imported {len(relations)} relationships in {end_rels - start_rels:.2f} seconds")

    def clean_db(tx):
        tx.run("MATCH (n) DETACH DELETE n")
        logging.info("Database cleaned before import")

    with driver.session(database=db) as session:
        logging.info("Connected to Neo4j, starting import...")
        total_start = time.time()
        session.execute_write(clean_db)
        session.execute_write(import_data)
        total_end = time.time()

    driver.close()
    logging.info(f"Total import time: {total_end - total_start:.2f} seconds")
