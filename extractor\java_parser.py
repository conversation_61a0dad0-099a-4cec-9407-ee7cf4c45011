import os
import javalang
from collections import defaultdict
import json

import logging
def resolve_method_invocation(node, full_decl_name, method_name, import_map, class_field_types, declared_var_types, rel_path, add_relation):
    
    """
    Dynamically resolve method invocations based on variable or class context.
    Returns: (called_method_node, qualifier_var)
    """
    called_method_node = None
    qualifier = node.qualifier

    if qualifier:
        # Case: class-level variable
        if qualifier in class_field_types:
            imp_class = class_field_types[qualifier]
            called_method_node = f"method:{imp_class}.{node.member}"
            qualifier_var = f"variable:{full_decl_name}.{qualifier}"
            add_relation(qualifier_var, "instance_of", f"class:{imp_class}", rel_path)
            add_relation(qualifier_var, "calls", called_method_node, rel_path)
            return called_method_node, qualifier_var

        # Case: method-local variable
        elif qualifier in declared_var_types:
            type_name = declared_var_types[qualifier]
            if type_name in import_map:
                imp_class = import_map[type_name]
                called_method_node = f"method:{imp_class}.{node.member}"
                qualifier_var = f"variable:{full_decl_name}.{method_name}.{qualifier}"
                add_relation(qualifier_var, "instance_of", f"class:{imp_class}", rel_path)
                add_relation(qualifier_var, "calls", called_method_node, rel_path)
                return called_method_node, qualifier_var

        # Static/class name
        elif qualifier[0].isupper():
            called_method_node = f"method:{qualifier}.{node.member}"
            return called_method_node, f"static:{qualifier}"

        return None, None

    # No qualifier → assume local method
    called_method_node = f"method:{full_decl_name}.{node.member}"
    return called_method_node, None


def resolve_expression(expr, var_node, rel_path, add_relation):
    if isinstance(expr, javalang.tree.MethodInvocation):
        method_name = expr.member
        qualifier = expr.qualifier  # like "auth" or "jsonOutput"

        if qualifier:
            full_method = f"{qualifier}.{method_name}"
        else:
            full_method = method_name

        add_relation(var_node, "assigned_from", f"method:{full_method}", rel_path)

        # Recurse into deeper chains: auth.alternateJiraConnection().toString()
        if isinstance(expr.qualifier, javalang.tree.MethodInvocation):
            resolve_expression(expr.qualifier, f"method:{full_method}", rel_path, add_relation)

        # Also, track argument flows
        for arg in expr.arguments:
            if isinstance(arg, javalang.tree.MemberReference):
                add_relation(f"variable:{arg.member}", "flows_to", f"method:{full_method}", rel_path)

    elif isinstance(expr, javalang.tree.Cast):
        cast_type = getattr(expr.type, 'name', None)
        if cast_type:
            add_relation(var_node, "instance_of", f"class:{cast_type}", rel_path)
        resolve_expression(expr.expression, var_node, rel_path, add_relation)

    elif isinstance(expr, javalang.tree.ClassCreator):
        add_relation(var_node, "assigned_from", f"class:{expr.type.name}", rel_path)
        # Recurse into constructor arguments
        for arg in expr.arguments:
            resolve_expression(arg, var_node, rel_path, add_relation)

    elif isinstance(expr, javalang.tree.MemberReference):
        add_relation(f"variable:{expr.member}", "flows_to", var_node, rel_path)


def process_assignment(node, full_decl_name, method_name, class_fields, class_field_types, declared_vars, rel_path, add_relation,  import_map, interface_set):
    if not isinstance(node, javalang.tree.Assignment):
        return

    lhs = node.expressionl
    rhs = node.value

    # Extract variable name from LHS
    if isinstance(lhs, javalang.tree.MemberReference):
        var_name = lhs.member
    else:
        return  # unsupported assignment

    # Determine if it's a class field or method-local variable
    if var_name in class_fields:
        var_node = f"variable:{full_decl_name}.{var_name}"
        var_type = class_field_types.get(var_name)
    else:
        var_node = f"variable:{full_decl_name}.{method_name}.{var_name}"
        declared_vars.add(var_name)
        var_type = None  # Will infer if possible

    # === CASE 1: RHS is a cast expression ===
    # If RHS is a cast
    if isinstance(rhs, javalang.tree.Cast):
        cast_type = getattr(rhs.type, 'name', None)
        if cast_type:
            add_relation(var_node, "instance_of", f"class:{cast_type}", rel_path)

        inner = rhs.expression
        if isinstance(inner, javalang.tree.MethodInvocation):
            qualifier = inner.qualifier
            member = inner.member

            if qualifier and member:
                method_full = f"{qualifier}.{member}"
                add_relation(var_node, "assigned_from", f"method:{method_full}", rel_path)
                add_relation(f"variable:{qualifier}", "flows_to", f"method:{method_full}", rel_path)
                add_relation(f"method:{method_full}", "flows_to", var_node, rel_path)


    # === CASE 2: RHS is a method call ===
    elif isinstance(rhs, javalang.tree.MethodInvocation):
        resolve_expression(rhs, var_node, rel_path, add_relation)

    # === CASE 3: RHS is a new object ===
    elif isinstance(rhs, javalang.tree.ClassCreator):
        created_type = getattr(rhs.type, 'name', None)
        if created_type:
            add_relation(var_node, "assigned_from", f"class:{created_type}", rel_path)
            add_relation(var_node, "instance_of", f"class:{created_type}", rel_path)

    # === CASE 4: RHS is another variable (copy)
    elif isinstance(rhs, javalang.tree.MemberReference):
        source_var = rhs.member
        if source_var in class_fields:
            src_node = f"variable:{full_decl_name}.{source_var}"
        else:
            src_node = f"variable:{full_decl_name}.{method_name}.{source_var}"

        add_relation(src_node, "flows_to", var_node, rel_path)

        # Copy type if known
        src_type = class_field_types.get(source_var)
        if src_type:
            add_relation(var_node, "instance_of", f"class:{src_type}", rel_path)

    # === CASE 5: fallback — add type if known from field map
    elif var_type:
        if var_type in import_map:
            fq_type = import_map[var_type]
            if fq_type in interface_set:
                add_relation(var_node, "instance_of", f"interface:{fq_type}", rel_path)
            else:
                add_relation(var_node, "instance_of", f"class:{fq_type}", rel_path)


def process_constructor_assignment(node, full_decl_name, method_name, method_node,
                                   import_map, class_field_types, declared_var_types,
                                   rel_path, add_relation, class_fields):
    left = getattr(node, 'expressionl', None) or getattr(node, 'left', None)
    right = node.value

    if not isinstance(right, javalang.tree.ClassCreator):
        return

    if isinstance(left, javalang.tree.MemberReference):
        left_name = left.member
    elif isinstance(left, str):
        left_name = left
    else:
        return

    left_var = f"variable:{full_decl_name}.{method_name}.{left_name}"
    add_relation(method_node, "declares", left_var, rel_path)

    typename = right.type.name
    if typename in import_map:
        typename = import_map[typename]
    constructor_method = f"method:{typename}.<init>"

    # constructor assigns variable
    add_relation(constructor_method, "assigns", left_var, rel_path)
    add_relation(method_node, "calls", constructor_method, rel_path)
    add_relation(left_var, "instance_of", f"class:{typename}", rel_path)

    for arg in right.arguments:
        if isinstance(arg, javalang.tree.MemberReference):
            if arg.member in class_fields:
                src_var = f"variable:{full_decl_name}.{arg.member}"
            else:
                src_var = f"variable:{full_decl_name}.{method_name}.{arg.member}"
            add_relation(src_var, "flows_to", left_var, rel_path)
            add_relation(constructor_method, "uses", src_var, rel_path)
        elif isinstance(arg, str):
            literal_node = f"literal:{arg}"
            add_relation(literal_node, "flows_to", left_var, rel_path)


def process_static_method_invocation(node, full_decl_name, method_name, method_node, rel_path, add_relation, class_fields):
    if not isinstance(node, javalang.tree.MethodInvocation):
        return

    if node.qualifier and node.qualifier[0].isupper():
        # Likely a static method call: ClassName.method()
        class_name = node.qualifier
        method_called = f"method:{class_name}.{node.member}"
        add_relation(method_node, "calls", method_called, rel_path)

        for arg in node.arguments:
            if isinstance(arg, javalang.tree.MemberReference):
                if arg.member in class_fields:
                    arg_var = f"variable:{full_decl_name}.{arg.member}"  # ✅ class-level
                else:
                    arg_var = f"variable:{full_decl_name}.{method_name}.{arg.member}"
                add_relation(method_called, "uses", arg_var, rel_path)
            elif isinstance(arg, str):
                literal_node = f"literal:{arg}"
                add_relation(method_called, "uses", literal_node, rel_path)


def process_generic_element_type(node, full_decl_name, method_name, method_node, rel_path, add_relation, class_fields):

    if not isinstance(node, javalang.tree.LocalVariableDeclaration) and not isinstance(node, javalang.tree.FieldDeclaration):
        return

    declared_type = node.type
    if hasattr(declared_type, 'arguments') and declared_type.arguments:
        type_arg = declared_type.arguments[0]
        if hasattr(type_arg, 'type') and hasattr(type_arg.type, 'name'):
            element_type = type_arg.type.name
        elif hasattr(type_arg, 'name'):
            element_type = type_arg.name
        else:
            return

        for decl in node.declarators:
            
            var_name = decl.name
            if var_name in class_fields:
                full_var = f"variable:{full_decl_name}.{var_name}"  # ✅ class-level
            else:
              

                full_var = (
                    f"variable:{full_decl_name}.{method_name}.{var_name}"
                    if method_name else
                    f"variable:{full_decl_name}.{var_name}"
                )
            if var_name=="metricsSet": print(full_var, "element_type_of", f"class:{element_type}", rel_path)
            add_relation(full_var, "element_type_of", f"class:{element_type}", rel_path)

def process_class_fields(tree, full_decl_name, rel_path, add_relation, class_fields ):
    for _, node in tree.filter(javalang.tree.ClassDeclaration):
        for class_body_item in node.body:
            if isinstance(class_body_item, javalang.tree.FieldDeclaration):
                process_generic_element_type(
                    class_body_item,
                    full_decl_name,
                    method_name=None,
                    method_node=None,
                    rel_path=rel_path,
                    add_relation=add_relation,
                    class_fields = class_fields
                )


def extract_relations_to_json_javalang(project_path):
    logging.info(f"Scanning Java files in: {project_path}")
    
    nodes = {}  # metadata
    relations = []
    collection_entities = {}  # full class name → collection
    file_uses_collections = defaultdict(set)
    class_uses_classes = defaultdict(set)
    class_field_types = {}  # variable name → fully qualified class

    existing_relations = set()
    interface_set = set()

    def register_node(raw_node, file_path):
        if not raw_node or raw_node in nodes:
            return

        node_type, full_name = raw_node.split(":", 1)

        # Determine short name
        if node_type == "file":
            short_name = os.path.basename(full_name)
        elif node_type in ("folder", "project"):
            short_name = os.path.basename(full_name.rstrip("/\\"))
        else:
            short_name = full_name.split(".")[-1] if "." in full_name else full_name

        nodes[raw_node] = {
            "id": raw_node,
            "type": node_type,
            "name": short_name,
            "full_name": full_name,
            "file_path": file_path
        }

    def add_relation(src, rel, dst, file_path):
        if not src or not dst:
            return

        key = (src, rel, dst)
        if key in existing_relations:
            return

        existing_relations.add(key)
        register_node(src, file_path)
        register_node(dst, file_path)
        relations.append([src, rel, dst])


    project_name = os.path.basename(os.path.abspath(project_path))
    project_node = f"project:{project_name}"
    

    java_files = []
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        abs_root = os.path.join(project_path, rel_root)

        path_parts = rel_root.split(os.sep)

        # Determine the node type
        if rel_root == ".":
            current_node = project_node
        elif len(path_parts) == 1:
            current_node = f"application:{abs_root}"
            add_relation(project_node, "contains", current_node, abs_root)
        else:
            current_node = f"package:{abs_root}"
            parent_path = os.path.join(project_path, *path_parts[:-1])
            parent_node = (
                f"application:{parent_path}" if len(path_parts) == 2 else f"package:{parent_path}"
            )
            add_relation(parent_node, "contains", current_node, abs_root)

        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)
            subfolder_abs = os.path.join(project_path, subfolder_rel)
            sub_path_parts = subfolder_rel.split(os.sep)

            if len(sub_path_parts) == 1:
                sub_node = f"application:{subfolder_abs}"
            else:
                sub_node = f"package:{subfolder_abs}"

            add_relation(current_node, "contains", sub_node, abs_root)

        for file in files:
            if file.endswith(".java"):
                java_files.append(os.path.join(root, file))

    # Parse Java files
    parsed_files = {}
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            try:
                parsed_files[file_path] = javalang.parse.parse(f.read())
            except javalang.parser.JavaSyntaxError:
                continue

    # Class ↔ MongoDB collection mapping
    for file_path, tree in parsed_files.items():
        import_map = {}
        package_name = tree.package.name if tree.package else None

        for imp in tree.imports:
            if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")):
                class_name = imp.path.split('.')[-1]
                import_map[class_name] = imp.path

        for type_decl in tree.types:
            if not isinstance(type_decl, javalang.tree.ClassDeclaration):
                continue

            class_name = type_decl.name
            full_class_name = f"{package_name}.{class_name}" if package_name else class_name
            class_node = f"class:{full_class_name}"

            for annotation in type_decl.annotations:
                if annotation.name == "Document":
                    for pair in annotation.element:
                        if pair.name == "collection":
                            collection = pair.value.value
                            collection_entities[full_class_name] = collection
                            rel_path = os.path.relpath(file_path, project_path)
                            add_relation(class_node, "mapped_to_collection", f"collection:{collection}", rel_path)

    # File ↔ Folder mapping (updated to reflect project/app/package structure)
    for file_path, tree in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        abs_path = os.path.join(project_path, rel_path)

        folder_path = os.path.dirname(abs_path)
        folder_parts = os.path.relpath(folder_path, project_path).split(os.sep)

        if len(folder_parts) == 1:
            folder_node = f"application:{folder_path}"
        elif len(folder_parts) >= 2:
            folder_node = f"package:{folder_path}"
        else:
            folder_node = project_node

        file_node = f"file:{abs_path}"
        add_relation(folder_node, "contains", file_node, abs_path)


        # get all the imports and store in a dictionary
        import_map = {}
        package_name = tree.package.name if tree.package else None

        for imp in tree.imports:
            if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")): # skip wildcars *, and java imports
                class_name = imp.path.split('.')[-1]
                import_map[class_name] = imp.path

        for type_decl in tree.types: # high level decalrations like class and interface
            if isinstance(type_decl, javalang.tree.InterfaceDeclaration):
                full_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
                import_map[type_decl.name] = full_name
                interface_set.add(full_name)
            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                continue

            class_fields = set()

            for class_body_item in type_decl.body:
                if isinstance(class_body_item, javalang.tree.FieldDeclaration):
                    for decl in class_body_item.declarators:
                        class_fields.add(decl.name)


            decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            decl_node = f"{decl_type}:{full_decl_name}"
            add_relation(file_node, "declares", decl_node, rel_path) # file delcares class/ interface

            # (class → interface)
            if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.implements:
                for impl in type_decl.implements:
                    interface_name = impl.name
                    if interface_name in import_map:
                        impl_full = import_map[interface_name]
                        add_relation(decl_node, "implements", f"interface:{impl_full}", rel_path) # class implements interface

            # class Extends or interface extends
            if type_decl.extends:
                if isinstance(type_decl.extends, list):
                    for ext in type_decl.extends:
                        if ext.name in import_map:
                            ext_full = import_map[ext.name]
                            add_relation(decl_node, "extends", f"{decl_type}:{ext_full}", rel_path)
                else:
                    ext = type_decl.extends
                    if ext.name in import_map:
                        ext_full = import_map[ext.name]
                        add_relation(decl_node, "extends", f"{decl_type}:{ext_full}", rel_path)


            # direct class /interface variables
            for field in getattr(type_decl, "fields", []):
                for decl in field.declarators:
                    var_name = decl.name
                    var_node = f"variable:{full_decl_name}.{var_name}"
                    add_relation(decl_node, "has_variable", var_node, rel_path)


                    if hasattr(field.type, 'name') and field.type.name in import_map:
                        imp_class = import_map[field.type.name]
                        # add_relation(decl_node, "uses_class", f"class:{imp_class}", rel_path)
                        class_uses_classes[full_decl_name].add(imp_class)

                        class_field_types[var_name] = imp_class # added
                        # add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)
                        if imp_class in interface_set:
                            add_relation(var_node, "instance_of", f"interface:{imp_class}", rel_path)
                        else:
                            add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)

                        if imp_class in collection_entities:
                            collection = collection_entities[imp_class]
                            add_relation(var_node, "uses_collection", f"collection:{collection}", rel_path) # direct varaible uses collection

            process_class_fields(tree, full_decl_name, rel_path, add_relation, class_fields) # class level fields
            for method in getattr(type_decl, "methods", []):
                method_node = f"method:{full_decl_name}.{method.name}"
                add_relation(decl_node, "has_method", method_node, rel_path)

                if not method.body:
                    continue

                declared_var_types = {}


                # methods
                declared_vars = set()

                for path, node in method:
                    if isinstance(node, javalang.tree.FieldDeclaration):
                        print(f"Found FieldDeclaration: {node}")

                    
                    
                    if isinstance(node, javalang.tree.LocalVariableDeclaration):
                        
                        # echeck if variable is initialized by a method call
                        if decl.initializer and isinstance(decl.initializer, javalang.tree.MethodInvocation):
                            method_inv = decl.initializer
                            called_method_node = None

                            if method_inv.qualifier:
                                if method_inv.qualifier in declared_var_types:
                                    type_name = declared_var_types[method_inv.qualifier]
                                    if type_name in import_map:
                                        imp_class = import_map[type_name]
                                        called_method_node = f"method:{imp_class}.{method_inv.member}"
                                else:
                                    if method_inv.qualifier[0].isupper():
                                        # assume it's a class name
                                        called_method_node = f"method:{method_inv.qualifier}.{method_inv.member}"
                            else:
                                called_method_node = f"method:{full_decl_name}.{method_inv.member}"

                            if called_method_node:
                                add_relation(called_method_node, "assigns", var_node, rel_path)
                                add_relation(method_node, "calls", called_method_node, rel_path)

                        for decl in node.declarators:
                            declared_vars.add(decl.name)
                            var_name = decl.name
                            var_node = f"variable:{full_decl_name}.{method.name}.{var_name}"
                            add_relation(method_node, "uses", var_node, rel_path) # method uses variable

                            # get the variable type
                            if hasattr(node.type, 'name'):
                                type_name = node.type.name
                                declared_var_types[var_name] = type_name
                                # if the type is from imports
                                if type_name in import_map:
                                    imp_class = import_map[type_name]
                                    
                                    if imp_class in interface_set:
                                        add_relation(var_node, "instance_of", f"interface:{imp_class}", rel_path)
                                    else:
                                        add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)
                                    class_uses_classes[full_decl_name].add(imp_class)

                                # get method arguments
                                if hasattr(node.type, 'arguments') and node.type.arguments:
                                    for arg in node.type.arguments:
                                        if hasattr(arg, 'type') and hasattr(arg.type, 'name'):
                                            generic_type = arg.type.name
                                            if generic_type in import_map:
                                                imp_class = import_map[generic_type]
                                                
                                                if imp_class in interface_set:
                                                    add_relation(var_node, "instance_of", f"interface:{imp_class}", rel_path)
                                                else:
                                                    add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)
                            ## added
                            if decl.initializer and isinstance(decl.initializer, javalang.tree.MethodInvocation):
                                method_inv = decl.initializer
                                called_method_node = None

                                if method_inv.qualifier:
                                    qualifier = method_inv.qualifier

                                    # Check class-level fields
                                    if qualifier in class_field_types:
                                        imp_class = class_field_types[qualifier]
                                        called_method_node = f"method:{imp_class}.{method_inv.member}"

                                    elif qualifier in declared_var_types:
                                        # Local var with type
                                        type_name = declared_var_types[qualifier]
                                        if type_name in import_map:
                                            imp_class = import_map[type_name]
                                            called_method_node = f"method:{imp_class}.{method_inv.member}"

                                    elif qualifier[0].isupper():
                                        # Likely a class
                                        called_method_node = f"method:{qualifier}.{method_inv.member}"

                                else:
                                    # No qualifier → local method
                                    called_method_node = f"method:{full_decl_name}.{method_inv.member}"

                                if called_method_node:
                                    add_relation(called_method_node, "assigns", var_node, rel_path)
                                    add_relation(method_node, "calls", called_method_node, rel_path)
                                ## add done

                    # method calls
                    elif isinstance(node, javalang.tree.MethodInvocation):

                        
                        skip_standard = False
                        called_method_node = None
                        qualifier = node.qualifier

                        # Handle usage of class-level field variable in method
                        if qualifier in class_field_types:
                            imp_class = class_field_types[qualifier]
                            
                            # Construct the fully qualified variable node
                            field_var_node = f"variable:{full_decl_name}.{qualifier}"
                            
                            # method uses field variable
                            add_relation(method_node, "uses", field_var_node, rel_path)
                            
                            # field variable is instance_of imported class
                            add_relation(field_var_node, "instance_of", f"class:{imp_class}", rel_path)

                            # usage
                            class_uses_classes[full_decl_name].add(imp_class)


                        # method using field-scope or local variables
                        if qualifier:
                            if method.name:
                                var_node = f"variable:{full_decl_name}.{method.name}.{qualifier}"
                            else:
                                var_node = f"variable:{full_decl_name}.{qualifier}"
                            add_relation(method_node, "uses", var_node, rel_path)

                        # resolving field-scoped variables to class types
                        if qualifier in class_field_types:
                            imp_class = class_field_types[qualifier]
                            called_method_node = f"method:{imp_class}.{node.member}"
                            # map instance_of
                            qualifier_var = f"variable:{full_decl_name}.{qualifier}"
                            add_relation(qualifier_var, "instance_of", f"class:{imp_class}", rel_path)
                            class_uses_classes[full_decl_name].add(imp_class)

                        # If method is from known import (via local var)
                        elif qualifier and qualifier in declared_var_types:
                            type_name = declared_var_types[qualifier]
                            if type_name in import_map:
                                imp_class = import_map[type_name]
                                called_method_node = f"method:{imp_class}.{node.member}"
                            else:
                                skip_standard = True

                        # Handle static or unknown qualifiers
                        elif qualifier:
                            if qualifier[0].islower():
                                skip_standard = True
                            else:
                                called_method_node = f"method:{qualifier}.{node.member}"

                        # Local method (no qualifier)
                        else:
                            called_method_node = f"method:{full_decl_name}.{node.member}"

                        # Create calls relation
                        if not skip_standard and called_method_node:
                            add_relation(method_node, "calls", called_method_node, rel_path)

                        # Flow of args into qualifier
                        if qualifier and node.arguments:
                            for arg in node.arguments:
                                if isinstance(arg, javalang.tree.MemberReference):
                                    if arg.member in class_fields:
                                        src = f"variable:{full_decl_name}.{arg.member}"
                                    else:
                                        src = f"variable:{full_decl_name}.{method.name}.{arg.member}"
                                    if qualifier in class_fields:
                                        dst = f"variable:{full_decl_name}.{qualifier}"
                                    else:
                                        dst = f"variable:{full_decl_name}.{method.name}.{qualifier}"
                                    add_relation(src, "flows_to", dst, rel_path)

                        # Record what the called method uses
                        for arg in node.arguments:
                            if isinstance(arg, javalang.tree.MemberReference):
                                if arg.member in class_fields:
                                    var_node = f"variable:{full_decl_name}.{arg.member}"
                                else:
                                    var_node = f"variable:{full_decl_name}.{method.name}.{arg.member}"
                            elif isinstance(arg, str):
                                var_node = f"literal:{arg}"
                            else:
                                continue
                            if called_method_node:
                                add_relation(called_method_node, "uses", var_node, rel_path)

                        # Map qualifier var to its type (again for completeness)
                        if qualifier:
                            qualifier_var = f"variable:{full_decl_name}.{method.name}.{qualifier}"
                            if qualifier in declared_var_types:
                                type_name = declared_var_types[qualifier]
                                if type_name in import_map:
                                    imp_class = import_map[type_name]
                                    add_relation(qualifier_var, "instance_of", f"class:{imp_class}", rel_path)
                                    class_uses_classes[full_decl_name].add(imp_class)

                    # variable assignments
                    elif isinstance(node, javalang.tree.Assignment):
                        process_assignment(node, full_decl_name, method.name, class_fields, class_field_types, declared_vars, rel_path, add_relation, import_map, interface_set)
                        
                        process_constructor_assignment(node, full_decl_name, method.name, method_node,
                                   import_map, class_field_types, declared_var_types,
                                   rel_path, add_relation, class_fields)
    logging.info(f"Extracted {len(nodes)} nodes and {len(relations)} relations")
    return {
        "nodes": list(nodes.values()),
        "relations": relations
    }
